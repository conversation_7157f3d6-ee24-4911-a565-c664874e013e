<?php

namespace App\Http\Controllers\Customer\Dashboard\Profile;

use App\Http\Controllers\Controller;
use App\Services\ActivityLog;
use App\Services\Email\EmailSender;
use App\Utilities\RequestStatus;
use App\Utilities\RequestValidation;
use App\Utilities\ThrowException;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CustomerEmailVerificationController extends Controller
{
    /**
     * CustomerEmailVerificationController constructor.
     */
    public function __construct()
    {
        $this->activityLog = new ActivityLog();
    }

    /**
     * @return JsonResponse
     */
    public function code(): JsonResponse
    {
        try{
            $emailTemplate = new EmailSender();
            $code = generate_verification_code(6);

            me()->update(['email_verified' => $code]);

            $otpEmail = $emailTemplate->emailVerificationCode(['name' => me()->name, 'code' => $code]);
            email()->send_email(me()->email, 'Email Verification', $otpEmail);

            $this->activityLog->log('info', me()->name .' verification code is '.$code);
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::SUCCESS => 'E-mail verification has been sent successfully']);
    }

    /**
     * @param Request $request
     * @return false|JsonResponse|string
     */
    public function change(Request $request)
    {
        $valid = RequestValidation::validate($request, [
            'email' => 'required|email|max:255'
        ]);

        if($valid !== true){
            return $valid->getContent();
        }

        try{
            $emailTemplate = new EmailSender();

            $exists = user_manager()::getClass()->where('id', '!=', me()->id)->where('email', $request->email)->first();
            if($exists){
                return response()->json([RequestStatus::VALIDATION_ERROR => 'Sorry the email you provided is already in use.'], Response::HTTP_UNAUTHORIZED);
            }

            $code = generate_verification_code(6);

            me()->update(['email' => $request->email, 'email_verified' => $code]);

            $otpEmail = $emailTemplate->emailVerificationCode(['name' => me()->name, 'code' => $code]);
            email()->send_email(me()->email, 'Email Verification', $otpEmail);

            $this->activityLog->log('info', me()->name .' verification code is '.$code);
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::SUCCESS => 'E-mail verification has been sent successfully']);
    }

    /**
     * @param Request $request
     * @return false|JsonResponse|string
     */
    public function verify(Request $request)
    {
        $valid = RequestValidation::validate($request, [
            'code' => 'required|string'
        ]);

        if($valid !== true){
            return $valid->getContent();
        }

        try{
            if(me()->email_verified !== $request->code){
                return response()->json([RequestStatus::REQUEST_ERROR => 'Invalid verification code provided'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            me()->update(['email_verified' => null]);
            $this->activityLog->log('info', me()->name .' email verified');
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::SUCCESS => 'Your email has been verified successfully.']);
    }
}
