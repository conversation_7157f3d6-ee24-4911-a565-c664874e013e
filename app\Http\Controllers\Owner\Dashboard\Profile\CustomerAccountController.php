<?php

namespace App\Http\Controllers\Customer\Dashboard\Profile;

use App\Http\Controllers\Controller;
use App\Http\Resources\Customer\CustomerProfileResource;
use App\Models\User;
use App\Services\ActivityLog;
use App\Services\Email\EmailSender;
use App\Utilities\QueryStatus;
use App\Utilities\RequestStatus;
use App\Utilities\RequestValidation;
use App\Utilities\ThrowException;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CustomerAccountController extends Controller
{
    /**
     * CustomerAccountController constructor.
     */
    public function __construct()
    {
        $this->activityLog = new ActivityLog();
        $this->model = new User();
    }

    /**
     * @return JsonResponse
     */
    public function view(): JsonResponse
    {
        try{
            $user = ($this->model)->find(me()->id);
            $res = QueryStatus::check_found($user, 'Something is wrong with your account');
            if($res){
                return $res;
            }
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::DATA => new CustomerProfileResource($user)], Response::HTTP_OK);
    }

    /**
     * @param Request $request
     * @return false|JsonResponse|string
     */
    public function update(Request $request): bool|JsonResponse|string
    {
        $valid = RequestValidation::validate($request, [
            'name' => 'required|string',
            'email' => 'required|string',
            'phone' => 'required|string',
            'birthday' => 'nullable|string'
        ]);

        if($valid !== true){
            return $valid->getContent();
        }

        try{
            $emailExists = $this->model->withTrashed()->where('email', $request->email)->first();
            if($emailExists && $emailExists->id != me()->id){
                return response()->json([RequestStatus::ERROR => 'The email you selected is already in use.'], Response::HTTP_OK);
            }

            $phoneExists = $this->model->withTrashed()->where('phone', $request->phone)->first();
            if($phoneExists && $phoneExists->id != me()->id){
                return response()->json([RequestStatus::ERROR => 'The phone you selected is already in use.'], Response::HTTP_OK);
            }

            if($request->email !== me()->email){
                $emailTemplate = new EmailSender();
                $otpEmail = $emailTemplate->emailVerificationCode(['name' => me()->name, 'code' => me()->email_verified]);
                email()->send_email(me()->email, 'Email Verification', $otpEmail);
            }

            me()->update([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'birthday' => $request->birthday,
                'email_verified' => $request->email !== me()->email ? generate_verification_code(4) : me()->email_verified
            ]);

            $this->activityLog->log('info', $request->email_verified);
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::SUCCESS => 'Profile has been updated successfully'], Response::HTTP_OK);
    }

    /**
     * @param Request $request
     * @return false|JsonResponse|string
     */
    public function avatar(Request $request)
    {
        $valid = RequestValidation::validate($request, [
            'avatar' => 'required|string'
        ]);

        if($valid !== true){
            return $valid->getContent();
        }

        try{
            $image = file_uploader()->convert_base64_to_uploaded_file($request->avatar);
            $image_link = file_uploader()->upload('customers/'.me()->code.'/avatar', $image);

            me()->update([
                'avatar' => $image_link
            ]);
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::SUCCESS => 'Account avatar has been updated.'], Response::HTTP_OK);
    }

    /**
     * @param Request $request
     * @return false|JsonResponse|string
     */
    public function online(Request $request): bool|JsonResponse|string
    {
        $valid = RequestValidation::validate($request, [
            'status' => 'required|string'
        ]);

        if($valid !== true){
            return $valid->getContent();
        }

        try{
            me()->update(['online' => $request->status]);
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::SUCCESS => "Online status has been updated successfully"]);
    }
}
