<?php

namespace App\Http\Controllers\Core\Location;

use App\Http\Controllers\Controller;
use App\Http\Resources\Location\StateResource;
use App\Models\State;
use App\Utilities\QueryStatus;
use App\Utilities\RequestStatus;
use App\Utilities\ThrowException;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class StateController extends Controller
{
    /**
     * StateController constructor.
     */
    public function __construct()
    {
        $this->model = new State();
    }

    /**
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        try{
            $states = $this->model->with('cities')->get();
            $res = QueryStatus::check_empty($states, 'States not available at the moment');
            if($res){
                return $res;
            }
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::DATA => StateResource::collection($states)], Response::HTTP_OK);
    }

    /**
     * @param Request $request
     * @return bool|JsonResponse
     */
    public function view(Request $request): JsonResponse|bool
    {
        try{
            $state = $this->model->with('cities')->where('key', $request->state)->first();
            $res = QueryStatus::check_found($state, 'The selected state not available at the moment');
            if($res){
                return $res;
            }
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::DATA => new StateResource($state)], Response::HTTP_OK);
    }
}
