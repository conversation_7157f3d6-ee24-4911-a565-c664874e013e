<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Owner\OwnerAuth\{
    OwnerForgottenPasswordController,
    OwnerLoginController,
    OwnerRegistrationController
};

Route::group(['prefix' => 'owner', 'as' => 'owner.'], function() {
    // Owner Auth Routes
    Route::post('register', [OwnerRegistrationController::class, 'register'])->name('register');
    Route::post('verify-email', [OwnerRegistrationController::class, 'verifyEmail'])->name('verify-email');
    Route::post('resend-verification', [OwnerRegistrationController::class, 'resendVerificationCode'])->name('resend-verification');
    Route::post('login', [OwnerLoginController::class, 'login'])->name('login');
    
    // Password Reset Routes
    Route::group(['prefix' => 'password', 'as' => 'password.'], function() {
        Route::post('reset-code', [OwnerForgottenPasswordController::class, 'sendResetCode'])->name('reset-code');
        Route::post('verify-code', [OwnerForgottenPasswordController::class, 'verifyResetCode'])->name('verify-code');
        Route::post('reset', [OwnerForgottenPasswordController::class, 'resetPassword'])->name('reset');
    });
    
    // Protected Owner Routes
    Route::middleware('auth:sanctum')->group(function() {
        Route::post('logout', [OwnerLoginController::class, 'logout'])->name('logout');
    });
});

