<?php

namespace App\Http\Controllers\Owner\OwnerAuth;

use Exception;
use App\Models\Owner;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use App\Utilities\RequestStatus;
use Illuminate\Validation\Rules;
use App\Utilities\ThrowException;
use Illuminate\Http\JsonResponse;
use App\Services\Email\EmailSender;
use App\Http\Controllers\Controller;
use App\Utilities\RequestValidation;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Eloquent\Model;

class OwnerRegistrationController extends Controller
{
    protected string $guard;
    protected Model $model;

    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->guard = 'owner';
        $this->model = new Owner();
    }

    /**
     * Register a new owner account.
     *
     * @param Request $request
     * @return JsonResponse|false|string
     *
     * @throws Exception
     */
    public function register(Request $request)
    {
        $validated = RequestValidation::validate($request, [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:owners',
            'phone' => 'required|string|max:20|unique:owners',
            'password' => ['required', 'confirmed', Rules\Password::min(8)->mixedCase()->numbers()->symbols()]
        ]);

        if ($validated !== true) {
            return $validated->getContent();
        }

        try {
            $newUser = new $this->model();

            $userData = [
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'phone' => $request->phone,
                'password' => Hash::make($request->password),
                'code' => generate_unique_code($newUser, 10),
            ];

            $emailTemplate = new EmailSender();
            $code = generate_verification_code(6);

            $userData['email_verified'] = $code;
            $userData['send_next_otp_sms_after'] = now()->addMinutes(10);

            $owner = $this->model::create($userData);

            $notifiable = $owner->first_name . ' ' . $owner->last_name;
            $otpEmail = $emailTemplate->emailVerificationCode(['name' => $notifiable, 'code' => $code]);
            email()->send_email($owner->email, 'Email Verification', $otpEmail);

            return response()->json([RequestStatus::DATA => $owner], Response::HTTP_CREATED);
        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception);
        }
    }

    /**
     * Verify owner's email with OTP code.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @throws Exception
     */
    public function verifyEmail(Request $request): JsonResponse
    {
        $validated = RequestValidation::validate($request, [
            'email' => 'required|email',
            'code' => 'required|string|size:6'
        ]);

        if ($validated !== true) {
            return $validated;
        }

        try {
            $owner = $this->model::where('email', $request->email)->first();

            if (! $owner || $owner->email_verified !== $request->code || Carbon::parse($owner->send_next_otp_sms_after)->isPast()) {
                return response()->json([
                    RequestStatus::ERROR => 'Invalid or expired verification code'
                ], Response::HTTP_BAD_REQUEST);
            }

            $owner->update([
                'email_verified_at' => now(),
                'email_verified' => null,
                'send_next_otp_sms_after' => null,
            ]);

            $token = $owner->createToken("{$this->guard}-token")->plainTextToken;

            return response()->json([RequestStatus::DATA => ['owner' => $owner, 'token' => $token]], Response::HTTP_OK);
        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception);
        }
    }

    /**
     * Resend email verification OTP.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @throws Exception
     */
    public function resendVerificationCode(Request $request): JsonResponse
    {
        $validated = RequestValidation::validate($request, [
            'email' => 'required|email|exists:owners,email'
        ]);

        if ($validated !== true) {
            return $validated;
        }

        try {
            $user = $this->model::where('email', $request->email)->first();

            if (! $user || $user->email_verified === null) {
                return response()->json([
                    RequestStatus::ERROR => 'Unable to send verification code. Please try again later.'
                ], Response::HTTP_BAD_REQUEST);
            }

            $code = generate_verification_code(6);
            $emailTemplate = new EmailSender();

            $user->update([
                'email_verified' => $code,
                'send_next_otp_sms_after' => Carbon::now()->addMinutes(10),
            ]);

            $notifiable = $user->first_name . ' ' . $user->last_name;
            $otpEmail = $emailTemplate->emailVerificationCode(['name' => $notifiable, 'code' => $code]);
            email()->send_email($user->email, 'Email Verification', $otpEmail);

            return response()->json([
                RequestStatus::SUCCESS => 'Verification code sent successfully'
            ], Response::HTTP_OK);
        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception);
        }
    }
}
