<?php

namespace App\Http\Controllers\Customer\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Resources\Customer\ProductFavoriteResource;
use App\Http\Resources\Customer\FavoriteApartmentResource;
use App\Models\Apartment;
use App\Models\Favorite;
use App\Models\Product;
use App\Utilities\QueryStatus;
use App\Utilities\RequestStatus;
use App\Utilities\RequestValidation;
use App\Utilities\ThrowException;
use App\Vendor;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CustomerFavoriteController extends Controller
{
    /**
     * CustomerFavoriteController constructor.
     */
    public function __construct()
    {
        $this->model = new Favorite();
    }

    /**
     * @return bool|JsonResponse
     */
    public function list(): JsonResponse|bool
    {
        try{
            $favorites = me()->favorites()->with(['vendor', 'vendor.city', 'vendor.state'])->whereNotNull('vendor_id')->paginate(20);
            $res = QueryStatus::check_empty($favorites, 'You do not have any vendor favorites');
            if($res){
                return $res;
            }
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::DATA => FavoriteApartmentResource::collection($favorites)], Response::HTTP_OK);
    }

    /**
     * @param Request $request
     * @return bool|JsonResponse|string
     */
    public function add(Request $request): JsonResponse|bool|string
    {
        $valid = RequestValidation::validate($request, [
            'vendor' => 'required|string'
        ]);

        if($valid !== true){
            return $valid->getContent();
        }

        try{
            $vendor = Vendor::where('code', $request->vendor)->first();
            $res = QueryStatus::check_found($vendor, 'The vendor you are looking for could not be found');
            if($res){
                return $res;
            }

            $exists = me()->favorites()->where('vendor_id', $vendor->id)->first();
            if($exists){
                return response()->json([RequestStatus::ERROR => $vendor->business_name .' has already been added to your list'], Response::HTTP_OK);
            }

            $this->model->create([
                'vendor_id' => $vendor->id,
                'user_id' => me()->id,
                'code' => generate_unique_code($this->model, 7)
            ]);
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::SUCCESS => $vendor->business_name .' has been added to your favorites'], Response::HTTP_OK);
    }

    /**
     * @param Request $request
     * @return bool|JsonResponse|string
     */
    public function remove_favorite(Request $request): JsonResponse|bool|string
    {
        $valid = RequestValidation::validate($request, [
            'type' => 'required|string',
            'code' => 'required|string'
        ]);

        if($valid !== true){
            return $valid->getContent();
        }

        try{
            $apartment = Apartment::where('code', $request->code)->first();
            $res = QueryStatus::check_found($apartment, 'The vendor you are looking for could not be found');
            if($res){
                return $res;
            }

            $favorite = me()->favorites()->where('apartment_id', $apartment->id)->first();
            $res = QueryStatus::check_found($favorite, 'The favorite you are looking for could not be found');
            if($res){
                return $res;
            }

            $favorite->delete();
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::SUCCESS => 'Favorite has been deleted.'], Response::HTTP_OK);
    }
}
