<?php

namespace App\Http\Controllers\Customer\CustomerAuth;

use Exception;
use Carbon\Carbon;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Utilities\RequestStatus;
use App\Utilities\ThrowException;
use Illuminate\Http\JsonResponse;
use App\Services\Email\EmailSender;
use App\Http\Controllers\Controller;
use App\Utilities\RequestValidation;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Eloquent\Model;

class CustomerForgottenPasswordController extends Controller
{
    protected string $guard;
    protected Model $model;

    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->guard = 'user';
        $this->model = new User();
    }

    /**
     * Send password reset OTP code.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @throws Exception
     */
    public function sendResetCode(Request $request)
    {
        $validated = RequestValidation::validate($request, [
            'email' => 'required|email|exists:users,email'
        ]);

        if ($validated !== true) {
            return $validated;
        }

        try {
            $user = $this->model::where('email', $request->email)->first();

            if (! $user) {
                return response()->json([
                    RequestStatus::ERROR => 'No account found with this email address'
                ], Response::HTTP_BAD_REQUEST);
            }

            $code = generate_verification_code(6);
            $emailTemplate = new EmailSender();

            $user->update([
                'password_reset_code' => $code,
                'send_next_otp_sms_after' => Carbon::now()->addMinutes(10)
            ]);

            $notifiable = $user->first_name . ' ' . $user->last_name;
            $resetEmail = $emailTemplate->passwordResetCode(['name' => $notifiable, 'code' => $code]);
            email()->send_email($user->email, 'Password Reset Code', $resetEmail);

            return response()->json([
                RequestStatus::SUCCESS => 'Password reset code has been sent to your email'
            ], Response::HTTP_OK);
        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception);
        }
    }

    /**
     * Verify password reset OTP code.
     * 
     * @param Request $request
     * @return JsonResponse
     * 
     * @throws Exception
     */
    public function verifyResetCode(Request $request)
    {
        $validated = RequestValidation::validate($request, [
            'email' => 'required|email|exists:users,email',
            'code' => 'required|string|size:6',
        ]);

        if ($validated !== true) {
            return $validated;
        }

        try {
            $user = $this->model::where('email', $request->email)
                ->where('password_reset_code', $request->code)
                ->first();

            if (! $user || Carbon::parse($user->send_next_otp_sms_after)->isPast()) {
                return response()->json([
                    RequestStatus::ERROR => 'Invalid or expired reset code'
                ], Response::HTTP_BAD_REQUEST);
            }

            return response()->json([
                RequestStatus::SUCCESS => 'Reset code verified successfully'
            ], Response::HTTP_OK);
        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception);
        }
    }

    /**
     * Reset password.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @throws Exception
     */
    public function resetPassword(Request $request)
    {
        $validated = RequestValidation::validate($request, [
            'email' => 'required|email|exists:users,email',
            'code' => 'required|string|size:6',
            'password' => 'required|string|min:8|confirmed',
            'password_confirmation' => 'required|same:password'
        ]);

        if ($validated !== true) {
            return $validated;
        }

        try {
            $user = $this->model::where('email', $request->email)
                ->where('password_reset_code', $request->code)
                ->first();

            if (! $user || Carbon::parse($user->send_next_otp_sms_after)->isPast()) {
                return response()->json([
                    RequestStatus::ERROR => 'Invalid or expired reset code'
                ], Response::HTTP_BAD_REQUEST);
            }

            $user->update([
                'password' => Hash::make($request->password),
                'password_reset_code' => null,
                'send_next_otp_sms_after' => null
            ]);

            return response()->json([
                RequestStatus::SUCCESS => 'Password reset successfully'
            ], Response::HTTP_OK);
        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception);
        }
    }
}
