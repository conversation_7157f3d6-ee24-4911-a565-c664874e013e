<?php

namespace App\Http\Controllers\Core\System;

use App\Http\Controllers\Controller;
use App\Http\Resources\System\FaqResource;
use App\Http\Resources\System\SettingResource;
use App\Models\Faq;
use App\Utilities\QueryStatus;
use App\Utilities\RequestStatus;
use App\Utilities\ThrowException;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class SettingsController extends Controller
{
    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try{
            $settings = get_settings();
            $res = QueryStatus::check_empty($settings, 'System settings not available at the moment');
            if($res){
                return $res;
            }
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::DATA => new SettingResource($settings)], Response::HTTP_OK);
    }

    /**
     * @return JsonResponse
     */
    public function faqs(): JsonResponse
    {
        return response()->json([
            RequestStatus::DATA => [
                'categories' => config('custom.faqcategories'),
                'faqs' => FaqResource::collection(Faq::all())
            ]
        ], Response::HTTP_OK);
    }
}
