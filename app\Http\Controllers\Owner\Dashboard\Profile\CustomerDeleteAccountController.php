<?php

namespace App\Http\Controllers\Customer\Dashboard\Profile;

use App\Http\Controllers\Controller;
use App\Models\DeletedAccount;
use App\Models\User;
use App\Utilities\QueryStatus;
use App\Utilities\RequestStatus;
use App\Utilities\RequestValidation;
use App\Utilities\ThrowException;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Hash;

class CustomerDeleteAccountController extends Controller
{
    /**
     * DeleteAccountController constructor.
     */
    public function __construct()
    {
        $this->middleware('auth:sanctum');
    }

    /**
     * @param Request $request
     * @return false|JsonResponse|string
     */
    public function temporary(Request $request): bool|JsonResponse|string
    {
        $valid = RequestValidation::validate($request, [
            'password' => 'required|max:120|string'
        ]);

        if($valid !== true){
            return $valid->getContent();
        }

        try{
            if(!Hash::check($request->password, me()->password)){
                return response()->json([RequestStatus::ERROR => 'Password is incorrect'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            email()->send_email(me()->email, 'Account Deletion', 'Account has been temporary suspended');

            me()->tokens()->delete();
            me()->delete();
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::SUCCESS => "Your account has been deleted temporarily"], Response::HTTP_OK);
    }

    /**
     * @param Request $request
     * @return false|JsonResponse|string
     */
    public function permanent(Request $request): bool|JsonResponse|string
    {
        $valid = RequestValidation::validate($request, [
            'password' => 'required|max:120|string',
            'reason' => 'required|string'
        ]);

        if($valid !== true){
            return $valid->getContent();
        }

        try{
            if(!Hash::check($request->password, me()->password)){
                return response()->json([RequestStatus::ERROR => 'Password is incorrect'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            me()->update([
                'deleted_at' => now()
            ]);

            DeletedAccount::create([
                'user_id' => me()->id,
                'reason' => $request->reason,
                'code' => generate_unique_code(new DeletedAccount(), 10)
            ]);

            // send email
            email()->send_email(me()->email, 'Account Deletion', 'Account has been scheduled for permanent deletion');

            me()->tokens()->delete();
            me()->delete();
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::SUCCESS => "Your account has been deleted temporarily"], Response::HTTP_OK);
    }

    /**
     * @param Request $request
     * @return bool|JsonResponse|string
     */
    public function restore(Request $request): JsonResponse|bool|string
    {
        $valid = RequestValidation::validate($request, [
            'email' => 'required|email',
            'phone' => 'required|string'
        ]);

        if($valid !== true){
            return $valid->getContent();
        }

        try{
            $user = User::withTrashed()->where('email', $request->email)->where('phone', $request->phone)->first();
            $res = QueryStatus::check_found($user, 'The selected account could not be found.');
            if($res){
                return $res;
            }

            $code = generate_unique_code(new User(), 6);

            $user->update([
                'deleted_at' => null,
                'password' => bcrypt($code),
                'dfsdasdeder' => encrypt($code)
            ]);

            email()->send_email($user->email, 'Restore Account Code', 'Use the following temporary password to login to your account, you are advised to change it immediately after login: '.$code);
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::SUCCESS => "Please check your email for your temporary password"], Response::HTTP_OK);
    }
}
