<?php

namespace App\Http\Controllers\Customer\CustomerAuth;

use Exception;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Utilities\RequestStatus;
use Illuminate\Validation\Rules;
use App\Utilities\ThrowException;
use Illuminate\Http\JsonResponse;
use App\Services\Email\EmailSender;
use App\Http\Controllers\Controller;
use App\Utilities\RequestValidation;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Eloquent\Model;

class CustomerRegistrationController extends Controller
{
    protected string $guard;
    protected Model $model;

    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->guard = 'user';
        $this->model = new User();
    }

    /**
     * Register a new user account.
     *
     * @param Request $request
     * @return JsonResponse|false|string
     *
     * @throws Exception
     */
    public function register(Request $request)
    {
        $validated = RequestValidation::validate($request, [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users',
            'phone' => 'required|string|max:20|unique:users',
            'password' => ['required', 'confirmed', Rules\Password::min(8)->mixedCase()->numbers()->symbols()]
        ]);

        if ($validated !== true) {
            return $validated->getContent();
        }

        try {
            $newUser = new $this->model();

            $userData = [
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'phone' => $request->phone,
                'password' => Hash::make($request->password),
                'code' => generate_unique_code($newUser, 10),
            ];

            $user = $this->model::create($userData);

            $token = $user->createToken("{$this->guard}-token")->plainTextToken;

            return response()->json([RequestStatus::DATA => ['user' => $user, 'token' => $token]], Response::HTTP_CREATED);
        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception);
        }
    }
}
