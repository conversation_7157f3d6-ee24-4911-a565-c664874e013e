<?php

namespace App\Utilities;

use App\Services\ActivityLog;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

class ThrowException
{
    /**
     * @var ActivityLog
     */
    protected ActivityLog $activityLog;

    /**
     * ThrowException constructor.
     */
    public function __construct()
    {
        $this->activityLog = new ActivityLog();
    }

    /**
     * @param \Exception $exception
     * @return JsonResponse
     */
    public function throw(\Exception $exception): JsonResponse
    {
        $this->activityLog->log('info', $exception->getMessage() .': '. $exception->getFile() .': Line '. $exception->getLine());
        return response()->json([RequestStatus::ERROR => $exception->getMessage() .': '. $exception->getFile() .': Line '. $exception->getLine()], Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
