<?php

namespace App\Http\Resources\Customer;

use App\Http\Resources\General\VendorResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FavoriteApartmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'code' => $this->code,
            'vendor' => new VendorResource($this->whenLoaded('vendor'))
        ];
    }
}
