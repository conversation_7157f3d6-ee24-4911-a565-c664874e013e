<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Str;
use App\Services\Email\EmailSender;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use App\Mail\EmailVerificationOtpMail;
use App\Utilities\RequestStatus;

/**
 * AuthService handles authentication logic for different user types (admin, owner, user).
 *
 * Responsibilities:
 * - Registration with OTP email verification
 * - OTP verification
 * - Login with email verification check
 * - Logout (token revocation)
 * - Resend OTP for email verification
 * - Password reset functionality
 */
class AuthService
{
    /**
     * The Eloquent model class for the current guard.
     * @var string
     */
    protected $model;

    /**
     * The guard type (e.g., 'admin', 'owner', 'user').
     * @var string
     */
    protected $guard;

    /**
     * Whether the guard requires OTP verification.
     * @var string
     */
    protected $requiresOtp;

    /**
     * Initialize AuthService with a guard type.
     *
     * @param string $guard
     */
    public function __construct(string $guard, bool $requiresOtp = true)
    {
        $this->guard = $guard;
        $this->requiresOtp = $requiresOtp;

        // Select the model class based on the guard
        $this->model = match ($guard) {
            'admin' => \App\Models\Admin::class,
            'owner' => \App\Models\Owner::class,
            default  => \App\Models\User::class,
        };
    }

    /**
     * Register a new user/admin/owner and send OTP for email verification.
     *
     * @param array $data
     * @return mixed Newly created user instance
     */    public function register(array $data)
    {        
        $newUser = new $this->model();
        
        $userData = [
            'email' => $data['email'],
            'phone' => $data['phone'] ?? null,
            'password' => Hash::make($data['password']),
            'code' => generate_unique_code($newUser, 10),
        ];

        // Handle name fields based on guard type
        if (isset($data['name'])) {
            $userData['name'] = $data['name'];
        }
        if (isset($data['first_name'], $data['last_name'])) {
            $userData['first_name'] = $data['first_name'];
            $userData['last_name'] = $data['last_name'];
        }

        if ($this->requiresOtp) {
            $emailTemplate = new EmailSender();
            $code = generate_verification_code(6);

            $userData['email_verified'] = $code;
            $userData['send_next_otp_sms_after'] = now()->addMinutes(10);
        }

        $user = $this->model::create($userData);

        // Send OTP email for verification
        if ($this->requiresOtp) {
            $notifiable = $user->name ?? "$user->first_name $user->last_name";
            $otpEmail = $emailTemplate->emailVerificationCode(['name' => $notifiable, 'code' => $code]);
            email()->send_email($user->email, 'Email Verification', $otpEmail);
            return $user;
        }

        // Create token if OTP is not required
        if (property_exists($user, 'last_login')) {
            $user->update(['last_login' => now()]);
        }

        $token = $user->createToken("{$this->guard}-token")->plainTextToken;
        return [
            'user' => $user,
            'token' => $token
        ];
    }

    /**
     * Verify the OTP for email verification.
     *
     * @param string $email
     * @param string $otp
     * @return mixed|null User instance if verified, null otherwise
     */
    public function verifyOtp(string $email, string $otp)
    {
        $user = $this->model::where('email', $email)->first();

        if (! $user || $user->email_verified !== $otp || Carbon::parse($user->send_next_otp_sms_after)->isPast()) {
            return null;
        }

        $user->update([
            'email_verified_at' => now(),
            'email_verified' => null,
            'send_next_otp_sms_after' => null,
        ]);

        // TODO Create and return access token after verification - To be confirmed

        return $user;
    }

    /**
     * Attempt to log in a user/admin/owner.
     *
     * @param array $credentials ['email' => ..., 'password' => ...]
     * @return array ['token' => ..., 'user' => ...] or ['error' => ...]
     */
    public function login(array $credentials)
    {
        $user = $this->model::where('email', $credentials['email'])->first();

        // Check credentials
        if (! $user || ! Hash::check($credentials['password'], $user->password)) {
            return [RequestStatus::ERROR => 'Invalid credentials'];
        }

        // Ensure email is verified
        if ($this->requiresOtp && ! $user->email_verified_at) {
            return [RequestStatus::ERROR => 'Please verify your email first'];
        }

        // Update last login timestamp if it exists oon the model
        if (property_exists($user, 'last_login')) {
            $user->update(['last_login' => now()]);
        }

        // Create Sanctum token
        $token = $user->createToken("{$this->guard}-token")->plainTextToken;

        return ['token' => $token, 'user' => $user];
    }

    /**
     * Logout the user by revoking all tokens.
     *
     * @param mixed $user
     * @return bool
     */
    public function logout($user)
    {
        $user->tokens()->delete();
        return true;
    }

    /**
     * Resend OTP for email verification if not yet verified.
     *
     * @param string $email
     * @return bool True if OTP sent, false otherwise
     */
    public function resendOtp(string $email)
    {
        if (! $this->requiresOtp) return false;

        $user = $this->model::where('email', $email)->first();

        // Only send if user exists and not verified
        if (! $user || $user->email_verified === null) return false;

        $emailTemplate = new EmailSender();
        $code = generate_verification_code(6);

        $user->update([
            'email_verified' => $code,
            'send_next_otp_sms_after' => Carbon::now()->addMinutes(10),
        ]);

        // Resend OTP email
        $notifiable = $user->name ?? "$user->first_name $user->last_name";
        $otpEmail = $emailTemplate->emailVerificationCode(['name' => $notifiable, 'code' => $code]);
        email()->send_email($user->email, 'Email Verification', $otpEmail);

        return true;
    }

    /**
     * Initiate password reset process by sending a reset code.
     *
     * @param string $email User's email address
     * @return array ['success' => bool, 'message' => string]
     */
    public function initiatePasswordReset(string $email)
    {
        $user = $this->model::where('email', $email)->first();

        if (!$user) {
            return [
                'success' => false,
                'message' => 'No account found with this email address'
            ];
        }

        $code = generate_verification_code(6);
        $emailTemplate = new EmailSender();

        $user->update([
            'password_reset_code' => $code,
            'send_next_otp_sms_after' => Carbon::now()->addMinutes(10)
        ]);

        $notifiable = $user->name ?? "$user->first_name $user->last_name";
        $resetEmail = $emailTemplate->passwordResetCode(['name' => $notifiable, 'code' => $code]);
        email()->send_email($user->email, 'Password Reset Code', $resetEmail);

        return [
            'success' => true,
            'message' => 'Password reset code has been sent to your email'
        ];
    }

    /**
     * Verify the password reset code.
     *
     * @param string $email User's email address
     * @param string $code Reset code
     * @return array ['success' => bool, 'message' => string]
     */
    public function verifyPasswordResetCode(string $email, string $code)
    {
        $user = $this->model::where('email', $email)
            ->where('password_reset_code', $code)
            ->first();

        if (!$user || Carbon::parse($user->send_next_otp_sms_after)->isPast()) {
            return [RequestStatus::ERROR => 'Invalid or expired reset code'];
        }

        return [RequestStatus::SUCCESS => 'Reset code verified successfully'];
    }

    /**
     * Reset the password using the verified reset code.
     *
     * @param string $email User's email address
     * @param string $code Reset code
     * @param string $newPassword New password
     * @return array ['success' => bool, 'message' => string]
     */
    public function resetPassword(string $email, string $code, string $newPassword)
    {
        $user = $this->model::where('email', $email)
            ->where('password_reset_code', $code)
            ->first();

        if (!$user || Carbon::parse($user->send_next_otp_sms_after)->isPast()) {
            return [RequestStatus::ERROR => 'Invalid or expired reset code'];
        }

        $user->update([
            'password' => Hash::make($newPassword),
            'password_reset_code' => null,
            'send_next_otp_sms_after' => null
        ]);

        // Revoke all existing tokens for security
        $user->tokens()->delete();

        $emailTemplate = new EmailSender();
        $notifiable = $user->name ?? "$user->first_name $user->last_name";
        $successEmail = $emailTemplate->resetPasswordSuccess(['name' => $notifiable]);
        email()->send_email($user->email, 'Password Reset Successful', $successEmail);

        return [RequestStatus::SUCCESS => 'Password has been reset successfully'];
    }
}
