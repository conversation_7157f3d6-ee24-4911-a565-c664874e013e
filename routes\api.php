<?php

use Illuminate\Support\Facades\Route;

use App\Http\Controllers\Core\{
    Location\CityController,
    Location\StateController,
    System\SettingsController
};
use App\Http\Controllers\Customer\CustomerAuth\{
    CustomerForgottenPasswordController,
    CustomerLoginController,
    CustomerRegistrationController
};

/** CORE API ROUTES */
Route::group(['namespace' => 'Core', 'prefix' => 'core', 'as' => 'core.'], function(){

    /** System Routes **/
    Route::group(['namespace' => 'System', 'prefix' => 'system', 'as' => 'system.'], function(){
        Route::get('/settings', [SettingsController::class, 'index'])->name('settings');
        Route::get('/faqs', [SettingsController::class, 'faqs'])->name('faqs');
    });

    /** Location Routes */
    Route::group(['namespace' => 'Location', 'prefix' => 'location', 'as' => 'location.'], function(){

        /** States Routes */
        Route::get('/states', [StateController::class, 'index'])->name('states.all');
        Route::get('/states/view', [StateController::class, 'view'])->name('states.view');

        /** Cities Routes */
        Route::get('/cities', [CityController::class, 'index'])->name('city.all');
        Route::get('/city/view', [CityController::class, 'view'])->name('city.view');
    });

});

/** CUSTOMER API ROUTES */
Route::group(['namespace' => 'Customer', 'prefix' => 'customer', 'as' => 'customer.'], function() {

    // Auth Routes
    Route::group(['namespace' => 'CustomerAuth'], function() {
        Route::post('register', [CustomerRegistrationController::class, 'register'])->name('register');
        Route::post('login', [CustomerLoginController::class, 'login'])->name('login');

        // Reset Password Routes
        Route::group(['prefix' => 'password', 'as' => 'password.'], function(){
            Route::post('reset-code', [CustomerForgottenPasswordController::class, 'sendResetCode'])->name('send-reset-code');
            Route::post('verify-code', [CustomerForgottenPasswordController::class, 'verifyResetCode'])->name('verify-reset-code');
            Route::post('reset', [CustomerForgottenPasswordController::class, 'resetPassword'])->name('reset-password');
        });
    });

    // Referrer Routes
    // Route::post('/referrer/check', [CustomerReferralController::class, 'check'])->name('referrer.check');

    // Delete Account
    // Route::group(['prefix' => 'delete', 'as' => 'delete'], function(){
    //     Route::post('/temporary', [CustomerDeleteAccountController::class, 'temporary'])->name('temporary');
    //     Route::post('/permanent', [CustomerDeleteAccountController::class, 'permanent'])->name('permanent');
    //     Route::post('/restore', [CustomerDeleteAccountController::class, 'restore'])->name('restore');
    // });

    Route::group(['middleware' => 'auth:sanctum'], function() {
        Route::post('logout', [CustomerLoginController::class, 'logout'])->name('logout');

        Route::group(['namespace' => 'Dashboard'], function() {

            // Route::get('/referrals', [CustomerReferralController::class, 'referrals'])->name('referrals');

            /** PROFILE ROUTES */
            // Route::group(['namespace' => 'Profile', 'prefix' => 'profile', 'as' => 'profile.'], function(){
            //     Route::get('view', [CustomerAccountController::class, 'view'])->name('view');
            //     Route::post('update', [CustomerAccountController::class, 'update'])->name('update');
            //     Route::post('avatar', [CustomerAccountController::class, 'avatar'])->name('avatar');
            //     Route::post('online', [CustomerAccountController::class, 'online'])->name('online');

            //     // Password Routes
            //     Route::post('/password/change', [CustomerPasswordController::class, 'change'])->name('password.change');

            //     // Email Verification Routes
            //     Route::group(['prefix' => 'email', 'as' => 'email.'], function(){
            //         Route::post('/code', [CustomerEmailVerificationController::class, 'code'])->name('otp');
            //         Route::post('/change', [CustomerEmailVerificationController::class, 'change'])->name('change');
            //         Route::post('/verify', [CustomerEmailVerificationController::class, 'verify'])->name('verify');
            //     });
            // });

            /** TRANSACTIONS ROUTES */
            // Route::group(['prefix' => 'transactions', 'as' => 'transactions.'], function(){
            //     Route::get('/all', [CustomerTransactionsController::class, 'all'])->name('all');
            //     Route::get('/view', [CustomerTransactionsController::class, 'view'])->name('view');
            // });


            /** NOTIFICATION ROUTES */
            // Route::group(['prefix' => 'notifications', 'as' => 'notifications.'], function(){
            //     Route::get('/all', [CustomerNotificationController::class, 'all'])->name('all');
            //     Route::get('/unread', [CustomerNotificationController::class, 'unread'])->name('unread');
            //     Route::get('/read', [CustomerNotificationController::class, 'read'])->name('read');
            //     Route::get('/read_all', [CustomerNotificationController::class, 'read_all'])->name('read_all');
            // });

            // Favorites Routes
            // Route::group(['prefix' => 'favorites', 'as' => 'favorites.'], function(){
            //     Route::get('list', [CustomerFavoriteController::class, 'list'])->name('list');
            //     Route::post('add', [CustomerFavoriteController::class, 'add'])->name('add');
            //     Route::post('remove', [CustomerFavoriteController::class, 'remove'])->name('remove');
            // });

        });
    });

});
