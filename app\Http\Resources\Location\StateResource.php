<?php

namespace App\Http\Resources\Location;

use App\Http\Resources\General\VendorResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'key' => $this->key,
            'id' => $this->id,
            'name' => $this->name,
            'alias' => $this->alias,
            'cities' => CityResource::collection($this->whenLoaded('cities')),
            'vendors_count' => $this->whenCounted('vendors'),
            'vendors' => VendorResource::collection($this->whenLoaded('vendors'))
        ];
    }
}
