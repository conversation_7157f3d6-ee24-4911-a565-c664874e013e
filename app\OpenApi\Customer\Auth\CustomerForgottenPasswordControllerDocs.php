<?php

namespace App\OpenApi\Customer\Auth;

use OpenApi\Attributes as OA;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Customer\CustomerAuth\CustomerForgottenPasswordController;

#[OA\Tag(
    name: 'Customer Authentication',
    description: 'API endpoints for customer authentication operations'
)]
class CustomerForgottenPasswordControllerDocs extends CustomerForgottenPasswordController
{
    #[OA\Post(
        path: '/api/customer/password/reset-code',
        operationId: 'customerSendResetCode',
        summary: 'Send password reset code',
        description: 'Send a password reset code to customer\'s email address',
        tags: ['Customer Authentication'],
        security: []
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            required: ['email'],
            properties: [
                new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Reset code sent successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'success', type: 'string', example: 'Password reset code has been sent to your email')
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Failed to send reset code',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'No account found with this email address')
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Validation errors',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'validation_error',
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'email',
                            type: 'array',
                            items: new OA\Items(type: 'string'),
                            example: ['The email field is required.']
                        )
                    ]
                )
            ]
        )
    )]
    public function sendResetCode(Request $request): JsonResponse
    {
        return parent::sendResetCode($request);
    }

    #[OA\Post(
        path: '/api/customer/password/verify-code',
        operationId: 'customerVerifyResetCode',
        summary: 'Verify reset code',
        description: 'Verify the password reset code sent to customer\'s email',
        tags: ['Customer Authentication'],
        security: []
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            required: ['email', 'code'],
            properties: [
                new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                new OA\Property(property: 'code', type: 'string', minLength: 6, maxLength: 6, example: '123456')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Code verified successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'success', type: 'string', example: 'Reset code verified successfully')
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Invalid or expired code',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Invalid or expired reset code')
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Validation errors',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'validation_error',
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'email',
                            type: 'array',
                            items: new OA\Items(type: 'string'),
                            example: ['The email field is required.']
                        ),
                        new OA\Property(
                            property: 'code',
                            type: 'array',
                            items: new OA\Items(type: 'string'),
                            example: ['The code field is required.']
                        )
                    ]
                )
            ]
        )
    )]
    public function verifyResetCode(Request $request): JsonResponse
    {
        return parent::verifyResetCode($request);
    }

    #[OA\Post(
        path: '/api/customer/password/reset',
        operationId: 'customerResetPassword',
        summary: 'Reset password',
        description: 'Reset customer password using verified reset code',
        tags: ['Customer Authentication'],
        security: []
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            required: ['email', 'code', 'password', 'password_confirmation'],
            properties: [
                new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                new OA\Property(property: 'code', type: 'string', minLength: 6, maxLength: 6, example: '123456'),
                new OA\Property(
                    property: 'password', 
                    type: 'string', 
                    format: 'password', 
                    example: 'NewStrongPass123!',
                    description: 'Must contain at least 8 characters, one uppercase letter, one lowercase letter, one number, and one symbol'
                ),
                new OA\Property(
                    property: 'password_confirmation', 
                    type: 'string', 
                    format: 'password', 
                    example: 'NewStrongPass123!',
                    description: 'Must match the password field'
                )
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Password reset successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'success', type: 'string', example: 'Password reset successfully')
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Invalid or expired code',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Invalid or expired reset code')
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Validation errors',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'validation_error',
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'password',
                            type: 'array',
                            items: new OA\Items(type: 'string'),
                            example: ['The password must contain at least one uppercase and one lowercase letter.']
                        )
                    ]
                )
            ]
        )
    )]
    public function resetPassword(Request $request): JsonResponse
    {
        return parent::resetPassword($request);
    }
}
