<?php

namespace App\OpenApi\Owner\Auth;

use OpenApi\Attributes as OA;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Owner\OwnerAuth\OwnerRegistrationController;

#[OA\Tag(
    name: 'Owner Authentication',
    description: 'API endpoints for owner authentication operations'
)]
class OwnerRegistrationControllerDocs extends OwnerRegistrationController
{
    #[OA\Post(
        path: '/api/owner/register',
        operationId: 'ownerRegister',
        summary: 'Register a new owner',
        description: 'Create a new owner account. Email verification required before login.',
        tags: ['Owner Authentication'],
        security: []
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            required: ['first_name', 'last_name', 'email', 'phone', 'password', 'password_confirmation'],
            properties: [
                new OA\Property(property: 'first_name', type: 'string', example: 'John'),
                new OA\Property(property: 'last_name', type: 'string', example: 'Doe'),
                new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                new OA\Property(property: 'phone', type: 'string', example: '+**********'),
                new OA\Property(
                    property: 'password', 
                    type: 'string', 
                    format: 'password', 
                    example: 'StrongPass123!',
                    description: 'Must contain at least 8 characters, one uppercase letter, one lowercase letter, one number, and one symbol'
                ),
                new OA\Property(property: 'password_confirmation', type: 'string', format: 'password', example: 'StrongPass123!')
            ]
        )
    )]
    #[OA\Response(
        response: 201,
        description: 'Owner registered successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'data',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'first_name', type: 'string', example: 'John'),
                        new OA\Property(property: 'last_name', type: 'string', example: 'Doe'),
                        new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                        new OA\Property(property: 'phone', type: 'string', example: '+**********'),
                        new OA\Property(property: 'created_at', type: 'string', format: 'date-time')
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Validation errors'
    )]
    public function register(Request $request): JsonResponse|false|string
    {
        return parent::register($request);
    }

    #[OA\Post(
        path: '/api/owner/verify-email',
        operationId: 'ownerVerifyEmail',
        summary: 'Verify owner email',
        description: 'Verify owner\'s email address using OTP code sent during registration',
        tags: ['Owner Authentication'],
        security: []
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            required: ['email', 'code'],
            properties: [
                new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                new OA\Property(property: 'code', type: 'string', minLength: 6, maxLength: 6, example: '123456')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Email verified successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'data',
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'owner',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer'),
                                new OA\Property(property: 'email_verified_at', type: 'string', format: 'date-time')
                            ]
                        )
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Invalid verification code',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Invalid or expired verification code')
            ]
        )
    )]
    public function verifyEmail(Request $request): JsonResponse
    {
        return parent::verifyEmail($request);
    }

    #[OA\Post(
        path: '/api/owner/resend-verification',
        operationId: 'ownerResendVerification',
        summary: 'Resend verification code',
        description: 'Resend email verification OTP code',
        tags: ['Owner Authentication'],
        security: []
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            required: ['email'],
            properties: [
                new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Verification code sent successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'success', type: 'string', example: 'Verification code sent successfully')
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Unable to send code',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Unable to send verification code. Please try again later.')
            ]
        )
    )]
    public function resendVerificationCode(Request $request): JsonResponse
    {
        return parent::resendVerificationCode($request);
    }
}
