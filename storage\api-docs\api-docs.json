{"openapi": "3.0.0", "info": {"title": "Shortlet API", "description": "API documentation for Shortlet Africa", "contact": {"name": "Shortlet Africa", "email": "<EMAIL>"}, "version": "1.0.0"}, "paths": {"/api/admin/password/send-reset-code": {"post": {"tags": ["Admin Au<PERSON>ntication"], "summary": "Send password reset code to admin's email", "description": "Sends a verification code to the admin's email for password reset", "operationId": "adminSendResetCode", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}}, "type": "object"}}}}, "responses": {"200": {"description": "Reset code sent successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "string", "example": "Password reset code has been sent to your email"}}, "type": "object"}}}}, "422": {"description": "Validation errors", "content": {"application/json": {"schema": {"properties": {"validation_error": {"properties": {"email": {"type": "array", "items": {"type": "string"}, "example": ["The email field is required."]}}, "type": "object"}}, "type": "object"}}}}}, "security": []}}, "/api/admin/password/verify-code": {"post": {"tags": ["Admin Au<PERSON>ntication"], "summary": "Verify password reset code", "description": "Validates the reset code sent to admin's email", "operationId": "adminVerifyResetCode", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email", "code"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "code": {"type": "string", "maxLength": 6, "minLength": 6, "example": "123456"}}, "type": "object"}}}}, "responses": {"200": {"description": "Code verified successfully", "content": {"application/json": {"schema": {"properties": {"data": {"properties": {"success": {"type": "string", "example": "Reset code verified successfully"}}, "type": "object"}}, "type": "object"}}}}, "422": {"description": "Validation errors"}}, "security": []}}, "/api/admin/password/reset": {"post": {"tags": ["Admin Au<PERSON>ntication"], "summary": "Reset admin password", "description": "Reset admin password using the verification code", "operationId": "adminResetPassword", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email", "code", "password", "password_confirmation"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "code": {"type": "string", "maxLength": 6, "minLength": 6, "example": "123456"}, "password": {"type": "string", "format": "password", "example": "NewPassword123!"}, "password_confirmation": {"type": "string", "format": "password", "example": "NewPassword123!"}}, "type": "object"}}}}, "responses": {"200": {"description": "Password reset successful", "content": {"application/json": {"schema": {"properties": {"data": {"properties": {"success": {"type": "string", "example": "Password has been reset successfully"}}, "type": "object"}}, "type": "object"}}}}, "422": {"description": "Validation errors"}}, "security": []}}, "/api/admin/login": {"post": {"tags": ["Admin Au<PERSON>ntication"], "summary": "Authenticate an admin user", "description": "Login with email and password to receive an authentication token", "operationId": "adminLogin", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "format": "password", "example": "YourPassword123!"}}, "type": "object"}}}}, "responses": {"200": {"description": "Successful login", "content": {"application/json": {"schema": {"properties": {"data": {"properties": {"token": {"type": "string", "example": "1|abcdef123456..."}, "admin": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Admin User"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "+**********"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "Invalid credentials", "content": {"application/json": {"schema": {"properties": {"error": {"type": "string", "example": "Invalid credentials"}}, "type": "object"}}}}, "422": {"description": "Validation errors", "content": {"application/json": {"schema": {"properties": {"validation_error": {"properties": {"email": {"type": "array", "items": {"type": "string"}, "example": ["The email field is required."]}, "password": {"type": "array", "items": {"type": "string"}, "example": ["The password field is required."]}}, "type": "object"}}, "type": "object"}}}}}, "security": []}}, "/api/admin/logout": {"post": {"tags": ["Admin Au<PERSON>ntication"], "summary": "Logout an admin user", "description": "Revoke the current authentication token", "operationId": "adminLogout", "responses": {"200": {"description": "Successfully logged out", "content": {"application/json": {"schema": {"properties": {"success": {"type": "string", "example": "Successfully logged out"}}, "type": "object"}}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Unauthenticated"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/register": {"post": {"tags": ["Admin Au<PERSON>ntication"], "summary": "Register a new admin", "description": "Create a new admin account and return an authentication token", "operationId": "adminRegister", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["name", "email", "phone", "password", "password_confirmation"], "properties": {"name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "+**********"}, "password": {"description": "Must contain at least 8 characters, one uppercase letter, one lowercase letter, one number, and one symbol", "type": "string", "format": "password", "example": "StrongPass123!"}, "password_confirmation": {"type": "string", "format": "password", "example": "StrongPass123!"}}, "type": "object"}}}}, "responses": {"201": {"description": "Admin registered successfully", "content": {"application/json": {"schema": {"properties": {"data": {"properties": {"token": {"type": "string", "example": "1|abcdef123456..."}, "admin": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "+**********"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}, "type": "object"}}, "type": "object"}}}}, "422": {"description": "Validation errors", "content": {"application/json": {"schema": {"properties": {"validation_error": {"properties": {"name": {"type": "array", "items": {"type": "string"}, "example": ["The name field is required."]}, "email": {"type": "array", "items": {"type": "string"}, "example": ["The email field is required."]}, "phone": {"type": "array", "items": {"type": "string"}, "example": ["The phone field is required."]}, "password": {"type": "array", "items": {"type": "string"}, "example": ["The password must be at least 8 characters."]}}, "type": "object"}}, "type": "object"}}}}}, "security": []}}, "/api/owner/password/reset-code": {"post": {"tags": ["Owner Authentication"], "summary": "Send password reset code", "description": "Send a password reset code to owner's email address", "operationId": "ownerSendResetCode", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}}, "type": "object"}}}}, "responses": {"200": {"description": "Reset code sent successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "string", "example": "Password reset code has been sent to your email"}}, "type": "object"}}}}, "400": {"description": "Failed to send reset code", "content": {"application/json": {"schema": {"properties": {"error": {"type": "string", "example": "No account found with this email address"}}, "type": "object"}}}}}, "security": []}}, "/api/owner/password/verify-code": {"post": {"tags": ["Owner Authentication"], "summary": "Verify reset code", "description": "Verify the password reset code sent to owner's email", "operationId": "ownerVerifyResetCode", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email", "code"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "code": {"type": "string", "maxLength": 6, "minLength": 6, "example": "123456"}}, "type": "object"}}}}, "responses": {"200": {"description": "Code verified successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "string", "example": "Reset code verified successfully"}}, "type": "object"}}}}, "400": {"description": "Invalid or expired code"}}, "security": []}}, "/api/owner/password/reset": {"post": {"tags": ["Owner Authentication"], "summary": "Reset password", "description": "Reset owner's password using the verified reset code", "operationId": "ownerResetPassword", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email", "code", "password", "password_confirmation"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "code": {"type": "string", "maxLength": 6, "minLength": 6, "example": "123456"}, "password": {"description": "Must contain at least 8 characters, one uppercase letter, one lowercase letter, and one number", "type": "string", "format": "password", "example": "NewPassword123!"}, "password_confirmation": {"type": "string", "format": "password", "example": "NewPassword123!"}}, "type": "object"}}}}, "responses": {"200": {"description": "Password reset successful", "content": {"application/json": {"schema": {"properties": {"success": {"type": "string", "example": "Password has been reset successfully"}}, "type": "object"}}}}, "400": {"description": "Reset failed", "content": {"application/json": {"schema": {"properties": {"error": {"type": "string", "example": "Invalid or expired reset code"}}, "type": "object"}}}}}, "security": []}}, "/api/owner/login": {"post": {"tags": ["Owner Authentication"], "summary": "Authenticate an owner", "description": "Login with email and password to receive an authentication token. Account must be approved.", "operationId": "owner<PERSON><PERSON><PERSON>", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "format": "password", "example": "YourPassword123!"}}, "type": "object"}}}}, "responses": {"200": {"description": "Successfully logged in", "content": {"application/json": {"schema": {"properties": {"data": {"properties": {"token": {"type": "string", "example": "1|abcdef123456..."}, "owner": {"properties": {"id": {"type": "integer", "example": 1}, "first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "+**********"}, "approved": {"type": "boolean", "example": true}, "rejected": {"type": "boolean", "example": false}, "email_verified_at": {"type": "string", "format": "date-time"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "Authentication failed", "content": {"application/json": {"schema": {"properties": {"error": {"type": "string", "example": "Your account is pending approval."}}, "type": "object"}}}}, "422": {"description": "Validation errors", "content": {"application/json": {"schema": {"properties": {"validation_error": {"properties": {"email": {"type": "array", "items": {"type": "string"}, "example": ["The email field is required."]}, "password": {"type": "array", "items": {"type": "string"}, "example": ["The password field is required."]}}, "type": "object"}}, "type": "object"}}}}}, "security": []}}, "/api/owner/logout": {"post": {"tags": ["Owner Authentication"], "summary": "Logout an owner", "description": "Revoke the current authentication token", "operationId": "ownerLogout", "responses": {"200": {"description": "Successfully logged out", "content": {"application/json": {"schema": {"properties": {"success": {"type": "string", "example": "Logout successful."}}, "type": "object"}}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Unauthenticated"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/owner/register": {"post": {"tags": ["Owner Authentication"], "summary": "Register a new owner", "description": "Create a new owner account. Email verification required before login.", "operationId": "ownerRegister", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["first_name", "last_name", "email", "phone", "password", "password_confirmation"], "properties": {"first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "+**********"}, "password": {"description": "Must contain at least 8 characters, one uppercase letter, one lowercase letter, one number, and one symbol", "type": "string", "format": "password", "example": "StrongPass123!"}, "password_confirmation": {"type": "string", "format": "password", "example": "StrongPass123!"}}, "type": "object"}}}}, "responses": {"201": {"description": "Owner registered successfully", "content": {"application/json": {"schema": {"properties": {"data": {"properties": {"id": {"type": "integer", "example": 1}, "first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "+**********"}, "created_at": {"type": "string", "format": "date-time"}}, "type": "object"}}, "type": "object"}}}}, "422": {"description": "Validation errors"}}, "security": []}}, "/api/owner/verify-email": {"post": {"tags": ["Owner Authentication"], "summary": "Verify owner email", "description": "Verify owner's email address using OTP code sent during registration", "operationId": "ownerVerifyEmail", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email", "code"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "code": {"type": "string", "maxLength": 6, "minLength": 6, "example": "123456"}}, "type": "object"}}}}, "responses": {"200": {"description": "Email verified successfully", "content": {"application/json": {"schema": {"properties": {"data": {"properties": {"owner": {"properties": {"id": {"type": "integer"}, "email_verified_at": {"type": "string", "format": "date-time"}}, "type": "object"}}, "type": "object"}}, "type": "object"}}}}, "400": {"description": "Invalid verification code", "content": {"application/json": {"schema": {"properties": {"error": {"type": "string", "example": "Invalid or expired verification code"}}, "type": "object"}}}}}, "security": []}}, "/api/owner/resend-verification": {"post": {"tags": ["Owner Authentication"], "summary": "Resend verification code", "description": "Resend email verification OTP code", "operationId": "ownerResendVerification", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}}, "type": "object"}}}}, "responses": {"200": {"description": "Verification code sent successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "string", "example": "Verification code sent successfully"}}, "type": "object"}}}}, "400": {"description": "Unable to send code", "content": {"application/json": {"schema": {"properties": {"error": {"type": "string", "example": "Unable to send verification code. Please try again later."}}, "type": "object"}}}}}, "security": []}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "bearerFormat": "JWT", "scheme": "bearer"}}}, "tags": [{"name": "Admin Au<PERSON>ntication", "description": "API endpoints for admin authentication operations"}, {"name": "Owner Authentication", "description": "API endpoints for owner authentication operations"}]}