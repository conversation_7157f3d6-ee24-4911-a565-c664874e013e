<?php

namespace App\Http\Controllers\Customer\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Resources\Customer\CustomerTransactionResource;
use App\Utilities\QueryStatus;
use App\Utilities\RequestStatus;
use App\Utilities\ThrowException;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CustomerTransactionsController extends Controller
{
    /**
     * @return bool|JsonResponse
     */
    public function all(): JsonResponse|bool
    {
        try{
            $transactions = me()->transactions()->paginate(20);
            $res = QueryStatus::check_empty($transactions, 'Wallet transactions are not available at the moment');
            if($res){
                return $res;
            }
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::DATA => CustomerTransactionResource::collection($transactions)], Response::HTTP_OK);
    }

    /**
     * @param Request $request
     * @return bool|JsonResponse
     */
    public function view(Request $request): JsonResponse|bool
    {
        try{
            $transaction = me()->wallet_transactions()->where('code', $request->transaction)->first();
            $res = QueryStatus::check_found($transaction, 'Wallet transactions are not available at the moment');
            if($res){
                return $res;
            }
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::DATA => new CustomerTransactionResource($transaction)], Response::HTTP_OK);
    }
}
