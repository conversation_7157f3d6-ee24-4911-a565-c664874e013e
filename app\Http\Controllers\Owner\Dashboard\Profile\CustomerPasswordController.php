<?php

namespace App\Http\Controllers\Customer\Dashboard\Profile;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\ActivityLog;
use App\Utilities\RequestStatus;
use App\Utilities\RequestValidation;
use App\Utilities\ThrowException;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Hash;

class CustomerPasswordController extends Controller
{
    /**
     * CustomerPasswordController constructor.
     */
    public function __construct()
    {
        $this->activityLog = new ActivityLog();
        $this->model = new User();
    }

    /**
     * @param Request $request
     * @return false|JsonResponse|string
     */
    public function change(Request $request): bool|JsonResponse|string
    {
        $valid = RequestValidation::validate($request, [
            'old_password' => 'required|max:120|string',
            'new_password' => 'required|max:120|min:6|string',
            'new_password_confirmation' => 'required|max:120|min:6|string|same:new_password',
        ]);

        if($valid !== true){
            return $valid->getContent();
        }

        try{
            if(Hash::check($request->old_password, me()->password)){
                me()->update([
                    'password' => bcrypt($request->new_password),
                    'dfsdasdeder' => encrypt($request->new_password)
                ]);
            }
            else {
                return response()->json([RequestStatus::ERROR => 'Old password is incorrect'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::SUCCESS => "Password updated successfully"], Response::HTTP_OK);
    }
}
