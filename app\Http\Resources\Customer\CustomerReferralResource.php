<?php

namespace App\Http\Resources\Customer;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerReferralResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        if(!$this->code){
            return [];
        }

        return [
            'code' => $this->code,
            'owner' => $this->owner,
            'person' => $this->person,
            'active' => $this->active,
            'amount' => $this->amount,
            'dates' => [
                'created' => $this->created_at,
                'updated' => $this->updated_at
            ]
        ];
    }
}
