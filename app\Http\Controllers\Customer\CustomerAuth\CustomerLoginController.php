<?php

namespace App\Http\Controllers\Customer\CustomerAuth;

use Exception;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Utilities\RequestStatus;
use App\Utilities\ThrowException;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Utilities\RequestValidation;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;

class CustomerLoginController extends Controller
{
    protected string $guard;
    protected Model $model;

    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->guard = 'user';
        $this->model = new User();
    }

    /**
     * Login user with email and password.
     *
     * @param Request $request
     * @return JsonResponse|false|string
     *
     * @throws Exception
     */
    public function login(Request $request)
    {
        $validated = RequestValidation::validate($request, [
            'email' => 'required|email|exists:users,email',
            'password' => 'required|string'
        ]);

        if ($validated !== true) {
            return $validated->getContent();
        }

        try {
            $user = User::where('email', $request->email)->first();

            // Check credentials
            if (!Auth::guard($this->guard)->attempt($request->only(['email', 'password']))) {
                return response()->json([
                    RequestStatus::ERROR => 'Invalid credentials.'
                ], Response::HTTP_UNAUTHORIZED);
            }

            // Update last login timestamp
            $user->update(['last_login' => now()]);

            // Create Sanctum token
            $token = $user->createToken("{$this->guard}-token")->plainTextToken;

            return response()->json([
                RequestStatus::DATA => [
                    'user' => $user,
                    'token' => $token,
                ]
            ], Response::HTTP_OK);
        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception);
        }
    }

    /**
     * Logout user and revoke token.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function logout(Request $request)
    {
        try {
            $user = $request->user($this->guard);
            $user->tokens()->delete();

            return response()->json([
                RequestStatus::SUCCESS => 'Logout successful.'
            ], Response::HTTP_OK);
        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception);
        }
    }
}
