<?php

namespace App\Http\Resources\Location;

use App\Http\Resources\General\VendorResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CityResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'code' => $this->code,
            'name' => $this->name,
            'slug' => $this->slug,
            'state' => new StateResource($this->whenLoaded('state')),
            'apartments' => []
        ];
    }
}
