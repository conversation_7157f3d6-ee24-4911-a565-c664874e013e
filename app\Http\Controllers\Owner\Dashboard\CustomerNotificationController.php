<?php

namespace App\Http\Controllers\Customer\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Resources\General\NotificationResource;
use App\Utilities\QueryStatus;
use App\Utilities\RequestStatus;
use App\Utilities\ThrowException;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CustomerNotificationController extends Controller
{
    /**
     * @return JsonResponse|bool
     */
    public function all(): JsonResponse|bool
    {
        try{
            $notifications = me()->system_notifications()->paginate(20);
            $res = QueryStatus::check_empty($notifications, 'You do not have any notifications at the moment.');
            if($res){
                return $res;
            }
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::DATA => NotificationResource::collection($notifications)], Response::HTTP_OK);
    }

    /**
     * @return JsonResponse|bool
     */
    public function unread(): JsonResponse|bool
    {
        try{
            $notifications = me()->system_notifications()->where('read', false)->paginate(20);
            $res = QueryStatus::check_empty($notifications, 'You do not have any notifications at the moment.');
            if($res){
                return $res;
            }
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::DATA => NotificationResource::collection($notifications)], Response::HTTP_OK);
    }

    /**
     * @param Request $request
     * @return JsonResponse|bool
     */
    public function read(Request $request): JsonResponse|bool
    {
        try{
            $notification = me()->system_notifications()->where('code', $request->code)->first();
            $res = QueryStatus::check_found($notification, 'The selected notification could not be found the moment.');
            if($res){
                return $res;
            }

            $notification->update(['read' => true]);
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::MESSAGE => 'Notification marked as read successfully'], Response::HTTP_OK);
    }

    /**
     * @param Request $request
     * @return bool|JsonResponse
     */
    public function read_all(Request $request): JsonResponse|bool
    {
        try{
            $notifications = me()->system_notifications()->get();
            $res = QueryStatus::check_empty($notifications, 'You do not have any notifications at the moment.');
            if($res){
                return $res;
            }

            foreach($notifications as $notification){
                $notification->update(['read' => true]);
            }
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::MESSAGE => 'Notifications marked as read successfully'], Response::HTTP_OK);
    }
}
