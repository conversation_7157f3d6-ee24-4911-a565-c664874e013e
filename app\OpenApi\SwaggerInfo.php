<?php

namespace App\OpenApi;

use OpenApi\Attributes as OA;

#[OA\OpenApi(
    info: new OA\Info(
        version: "1.0.0",
        title: "Shortlet API",
        description: "API documentation for Shortlet Africa",
        contact: new OA\Contact(
            name: "Shortlet Africa",
            email: "<EMAIL>"
        ),
    
    ),
    
)]

#
# Security Scheme (JWT Bearer)
#
#[OA\SecurityScheme(
    securityScheme: "bearerAuth",
    type: "http",
    scheme: "bearer",
    bearerFormat: "JWT"
)]
class SwaggerInfo
{
    // 
}
