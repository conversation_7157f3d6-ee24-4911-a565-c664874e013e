<?php

namespace App\Http\Controllers\Owner\OwnerAuth;

use Exception;
use App\Models\Owner;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Utilities\RequestStatus;
use App\Utilities\ThrowException;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Utilities\RequestValidation;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;

class OwnerLoginController extends Controller
{
    protected string $guard;
    protected Model $model;

    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->guard = 'owner';
        $this->model = new Owner();
    }

    /**
     * Login owner with email and password.
     *
     * @param Request $request
     * @return JsonResponse|false|string
     *
     * @throws Exception
     */
    public function login(Request $request)
    {
        $validated = RequestValidation::validate($request, [
            'email' => 'required|email|exists:owners,email',
            'password' => 'required|string'
        ]);

        if ($validated !== true) {
            return $validated->getContent();
        }

        try {
            $owner = Owner::where('email', $request->email)->first();

            // Check credentials
            if (!Auth::guard($this->guard)->attempt($request->only(['email', 'password']))) {
                return response()->json([
                    RequestStatus::ERROR => 'Invalid credentials.'
                ], Response::HTTP_UNAUTHORIZED);
            }

            // TODO: Uncomment this when we have approval/rejection flow
            // if (!$owner->approved) {
            //     return response()->json([
            //         RequestStatus::ERROR => 'Your account is pending approval.'
            //     ], Response::HTTP_UNAUTHORIZED);
            // }

            // if ($owner->rejected) {
            //     return response()->json([
            //         RequestStatus::ERROR => 'Your account has been rejected. Please contact support.'
            //     ], Response::HTTP_UNAUTHORIZED);
            // }

            // Ensure email is verified
            if (! $owner->email_verified_at) {
                return response()->json([
                    RequestStatus::ERROR => 'Please verify your email first.'
                ], Response::HTTP_UNAUTHORIZED);
            }

            // Update last login timestamp
            $owner->update(['last_login' => now()]);

            // Create Sanctum token
            $token = $owner->createToken("{$this->guard}-token")->plainTextToken;

            return response()->json([
                RequestStatus::DATA => [
                    'owner' => $owner,
                    'token' => $token,
                ]
            ], Response::HTTP_OK);
        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception);
        }
    }

    /**
     * Logout owner and revoke token.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function logout(Request $request): JsonResponse
    {
        try {
            $user = $request->user($this->guard);
            $user->tokens()->delete();

            return response()->json([
                RequestStatus::SUCCESS => 'Logout successful.'
            ], Response::HTTP_OK);
        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception);
        }
    }
}
