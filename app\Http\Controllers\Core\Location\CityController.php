<?php

namespace App\Http\Controllers\Core\Location;

use App\Http\Controllers\Controller;
use App\Http\Resources\Location\CityResource;
use App\Models\City;
use App\Utilities\QueryStatus;
use App\Utilities\RequestStatus;
use App\Utilities\ThrowException;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CityController extends Controller
{
    /**
     * CityController constructor.
     */
    public function __construct()
    {
        $this->model = new City();
    }

    /**
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        try{
            $countries = $this->model->with('state')->get();
            $res = QueryStatus::check_empty($countries, 'Cities not available at the moment');
            if($res){
                return $res;
            }
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::DATA => CityResource::collection($countries)], Response::HTTP_OK);
    }

    /**
     * @param Request $request
     * @return bool|JsonResponse
     */
    public function view(Request $request): JsonResponse|bool
    {
        try{
            $city = $this->model->with('state')->where('slug', $request->city)->first();
            $res = QueryStatus::check_found($city, 'The selected city not available at the moment');
            if($res){
                return $res;
            }
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::DATA => new CityResource($city)], Response::HTTP_OK);
    }
}
