<?php

namespace App\Http\Resources\System;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SettingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'email' => $this->email,
            'phone' => $this->phone,
            'users' => $this->users,
            'owners' => $this->owners,
            'payment_gateway' => $this->payment_gateway,
            'affiliates' => $this->affiliates,
            'description' => $this->description,
            'send_sms' => $this->send_sms,
            'send_email' => $this->send_email,
            'facebook' => $this->facebook,
            'twitter' => $this->twitter,
            'tiktok' => $this->tiktok,
            'instagram' => $this->instagram,
            'whatsapp' => $this->whatsapp,
            'youtube' => $this->youtube,
            'currency' => config('custom.currency.ngn')
        ];
    }
}
