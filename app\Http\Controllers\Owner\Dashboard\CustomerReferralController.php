<?php

namespace App\Http\Controllers\Customer\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Resources\Customer\CustomerReferralResource;
use App\Models\User;
use App\Services\ActivityLog;
use App\Utilities\QueryStatus;
use App\Utilities\RequestStatus;
use App\Utilities\RequestValidation;
use App\Utilities\ThrowException;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CustomerReferralController extends Controller
{
    /**
     * CustomerReferralController constructor.
     */
    public function __construct()
    {
        $this->activityLog = new ActivityLog();
    }

    /**
     * @param Request $request
     * @return bool|JsonResponse|null
     */
    public function check(Request $request): JsonResponse|bool|null
    {
        $valid = RequestValidation::validate($request, [
            'phone' => 'required|string'
        ]);

        if($valid !== true){
            return $valid->getContent();
        }

        try{
            $user = User::where('code', $request->code)->first();
            $res = QueryStatus::check_found($user, 'The selected referrer could not be found, please check the phone number again.');
            if($res){
                return $res;
            }
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::DATA => $user->name]);
    }

    /**
     * @return bool|JsonResponse
     */
    public function referrals(): JsonResponse|bool
    {
        try{
            $referrals = me()->referrals()->get();
            $res = QueryStatus::check_empty($referrals, 'You do not have any referrals.');
            if($res){
                return $res;
            }
        }
        catch(Exception $exception){
            return (new ThrowException())->throw($exception);
        }

        return response()->json([RequestStatus::DATA => CustomerReferralResource::collection($referrals)], Response::HTTP_OK);
    }
}
