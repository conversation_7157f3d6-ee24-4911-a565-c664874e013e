<?php

namespace App\Http\Resources\Customer;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'code' => $this->code,
            'id' => $this->id,
            'name' => $this->name,
            'email' => [
                'verified' => !is_numeric($this->email_verified),
                'email' => $this->email
            ],
            'phone' => [
                'verified' => !is_numeric($this->phone_verified),
                'phone' => $this->phone
            ],
            'avatar' => $this->avatar ?? user_manager()->default_avatar($this->name),
            'gender' => $this->gender,
            'birthday' => $this->birthday,
            'balance' => [
                'main' => $this->balance,
                'affiliate' => $this->affiliate_balance,
                'points' => $this->points
            ],
            'permissions' => $this->permissions,
            'comment' => $this->comment,
            'referrer' => $this->referrer
        ];
    }
}
