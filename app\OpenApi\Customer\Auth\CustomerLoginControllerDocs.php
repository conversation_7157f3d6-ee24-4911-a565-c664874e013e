<?php

namespace App\OpenApi\Customer\Auth;

use OpenApi\Attributes as OA;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Customer\CustomerAuth\CustomerLoginController;

#[OA\Tag(
    name: 'Customer Authentication',
    description: 'API endpoints for customer authentication operations'
)]
class CustomerLoginControllerDocs extends CustomerLoginController
{
    #[OA\Post(
        path: '/api/customer/login',
        operationId: 'customerLogin',
        summary: 'Authenticate a customer',
        description: 'Login with email and password to receive an authentication token.',
        tags: ['Customer Authentication'],
        security: []
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            required: ['email', 'password'],
            properties: [
                new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                new OA\Property(property: 'password', type: 'string', format: 'password', example: 'YourPassword123!')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Successfully logged in',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'data',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'token', type: 'string', example: '1|abcdef123456...'),
                        new OA\Property(
                            property: 'user',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 1),
                                new OA\Property(property: 'first_name', type: 'string', example: 'John'),
                                new OA\Property(property: 'last_name', type: 'string', example: 'Doe'),
                                new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                                new OA\Property(property: 'phone', type: 'string', example: '+1234567890'),
                                new OA\Property(property: 'email_verified_at', type: 'string', format: 'date-time', nullable: true),
                                new OA\Property(property: 'flagged', type: 'boolean', example: false),
                                new OA\Property(property: 'last_login', type: 'string', format: 'date-time'),
                                new OA\Property(property: 'code', type: 'string', example: 'ABC123XYZ'),
                                new OA\Property(property: 'created_at', type: 'string', format: 'date-time'),
                                new OA\Property(property: 'updated_at', type: 'string', format: 'date-time')
                            ]
                        )
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: 'Authentication failed',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Invalid credentials.')
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Validation errors',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'validation_error',
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'email',
                            type: 'array',
                            items: new OA\Items(type: 'string'),
                            example: ['The email field is required.']
                        ),
                        new OA\Property(
                            property: 'password',
                            type: 'array',
                            items: new OA\Items(type: 'string'),
                            example: ['The password field is required.']
                        )
                    ]
                )
            ]
        )
    )]
    public function login(Request $request): JsonResponse|false|string
    {
        return parent::login($request);
    }

    #[OA\Post(
        path: '/api/customer/logout',
        operationId: 'customerLogout',
        summary: 'Logout a customer',
        description: 'Revoke the current authentication token',
        tags: ['Customer Authentication'],
        security: [["bearerAuth" => []]]
    )]
    #[OA\Response(
        response: 200,
        description: 'Successfully logged out',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'success', type: 'string', example: 'Logout successful.')
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: 'Unauthenticated',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'message', type: 'string', example: 'Unauthenticated')
            ]
        )
    )]
    public function logout(Request $request): JsonResponse
    {
        return parent::logout($request);
    }
}
