<?php

namespace App\Http\Controllers\Owner\OwnerAuth;

use Exception;
use App\Models\Owner;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use App\Utilities\RequestStatus;
use Illuminate\Validation\Rules;
use App\Utilities\ThrowException;
use Illuminate\Http\JsonResponse;
use App\Services\Email\EmailSender;
use App\Http\Controllers\Controller;
use App\Utilities\RequestValidation;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Eloquent\Model;

class OwnerForgottenPasswordController extends Controller
{
    protected string $guard;
    protected Model $model;

    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->guard = 'owner';
        $this->model = new Owner();
    }

    /**
     * Send password reset OTP code.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @throws Exception
     */
    public function sendResetCode(Request $request): JsonResponse
    {
        $validated = RequestValidation::validate($request, [
            'email' => 'required|email|exists:owners,email'
        ]);

        if ($validated !== true) {
            return $validated;
        }

        try {
            $owner = $this->model::where('email', $request->email)->first();

            if (! $owner) {
                return response()->json([
                    RequestStatus::ERROR => 'No account found with this email address'
                ], Response::HTTP_BAD_REQUEST);
            }

            $code = generate_verification_code(6);
            $emailTemplate = new EmailSender();

            $owner->update([
                'password_reset_code' => $code,
                'send_next_otp_sms_after' => Carbon::now()->addMinutes(10)
            ]);

            $notifiable = $owner->first_name . ' ' . $owner->last_name;
            $resetEmail = $emailTemplate->passwordResetCode(['name' => $notifiable, 'code' => $code]);
            email()->send_email($owner->email, 'Password Reset Code', $resetEmail);

            return response()->json(
                $owner->email_verified_at
                    ? [RequestStatus::SUCCESS => 'Please check your email for the verification code.']
                    : [RequestStatus::ERROR => 'Please verify your email first.'], 
                $owner->email_verified_at ? Response::HTTP_OK : Response::HTTP_BAD_REQUEST
            );
        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception);
        }
    }

    /**
     * Verify password reset OTP code.
     * 
     * @param Request $request
     * @return JsonResponse
     * 
     * @throws Exception
     */
    public function verifyResetCode(Request $request): JsonResponse
    {
        $validated = RequestValidation::validate($request, [
            'email' => 'required|email|exists:owners,email',
            'code' => 'required|string|size:6',
        ]);

        if ($validated !== true) {
            return $validated;
        }

        try {
            $owner = $this->model::where('email', $request->email)
                ->where('password_reset_code', $request->code)
                ->first();

            if (! $owner || Carbon::parse($owner->send_next_otp_sms_after)->isPast()) {
                return response()->json([
                    RequestStatus::ERROR => 'Invalid or expired reset code'
                ], Response::HTTP_BAD_REQUEST);
            }
            
            return response()->json([
                RequestStatus::SUCCESS => 'Reset code verified successfully'
            ], Response::HTTP_OK);
        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception);
        }
    }

    /**
     * Reset password.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @throws Exception
     */
    public function resetPassword(Request $request): JsonResponse
    {
        $validated = RequestValidation::validate($request, [
            'email' => 'required|email|exists:owners,email',
            'code' => 'required|string|size:6',
            'password' => ['required', 'confirmed', Rules\Password::min(8)->mixedCase()->numbers()->symbols()],
            'password_confirmation' => 'required|same:password'
        ]);

        if ($validated !== true) {
            return $validated;
        }

        try {
            $owner = $this->model::where('email', $request->email)
                ->where('password_reset_code', $request->code)
                ->first();

            if (! $owner || Carbon::parse($owner->send_next_otp_sms_after)->isPast()) {
                return response()->json([
                    RequestStatus::ERROR => 'Invalid or expired reset code'
                ], Response::HTTP_BAD_REQUEST);
            }

            $owner->update([
                'password' => Hash::make($request->password),
                'password_reset_code' => null,
                'send_next_otp_sms_after' => null
            ]);

            return response()->json([
                RequestStatus::SUCCESS => 'Password reset successfully'
            ], Response::HTTP_OK);
        } catch (Exception $exception) {
            return (new ThrowException())->throw($exception);
        }
    }
}
