[2025-06-06 06:20:08] local.ERROR: Target class [OwnerAuth\LoginController] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [OwnerAuth\\LoginController] does not exist. at C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:914)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('OwnerAuth\\\\Login...')
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('OwnerAuth\\\\Login...', Array, true)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('OwnerAuth\\\\Login...', Array)
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('OwnerAuth\\\\Login...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(278): Illuminate\\Foundation\\Application->make('OwnerAuth\\\\Login...')
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\Route->getController()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\app\\Http\\Middleware\\HttpRedirect.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\HttpRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\app\\Http\\Middleware\\SecureHeaders.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SecureHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\...')
#52 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"OwnerAuth\\LoginController\" does not exist at C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:912)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(912): ReflectionClass->__construct('OwnerAuth\\\\Login...')
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('OwnerAuth\\\\Login...')
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('OwnerAuth\\\\Login...', Array, true)
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('OwnerAuth\\\\Login...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('OwnerAuth\\\\Login...', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(278): Illuminate\\Foundation\\Application->make('OwnerAuth\\\\Login...')
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\Route->getController()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\app\\Http\\Middleware\\HttpRedirect.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\HttpRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\app\\Http\\Middleware\\SecureHeaders.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SecureHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#52 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\...')
#53 {main}
"} 
[2025-06-06 06:25:55] local.ERROR: Method Illuminate\Validation\Validator::validateMessage does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Validation\\Validator::validateMessage does not exist. at C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php:1646)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(660): Illuminate\\Validation\\Validator->__call('validateMessage', Array)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(459): Illuminate\\Validation\\Validator->validateAttribute('password', 'Message')
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(494): Illuminate\\Validation\\Validator->passes()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\app\\Utilities\\RequestValidation.php(21): Illuminate\\Validation\\Validator->fails()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\app\\Http\\Controllers\\Owner\\OwnerAuth\\OwnerForgottenPasswordController.php(104): App\\Utilities\\RequestValidation::validate(Object(Illuminate\\Http\\Request), Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Owner\\OwnerAuth\\OwnerForgottenPasswordController->resetPassword(Object(Illuminate\\Http\\Request))
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('resetPassword', Array)
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Owner\\OwnerAuth\\OwnerForgottenPasswordController), 'resetPassword')
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\app\\Http\\Middleware\\HttpRedirect.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\HttpRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\app\\Http\\Middleware\\SecureHeaders.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SecureHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\...')
#47 {main}
"} 
[2025-06-13 15:40:04] local.ERROR: Class "CustomerRegisterController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"CustomerRegisterController\" does not exist at C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('CustomerRegiste...')
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 15)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-06-13 16:10:44] local.ERROR: Class "OwnerAuth\LoginController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"OwnerAuth\\LoginController\" does not exist at C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('OwnerAuth\\\\Login...')
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 30)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\projects\\fullstack\\shortlet-api\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
