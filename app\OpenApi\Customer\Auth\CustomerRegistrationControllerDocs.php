<?php

namespace App\OpenApi\Customer\Auth;

use OpenApi\Attributes as OA;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Customer\CustomerAuth\CustomerRegistrationController;

#[OA\Tag(
    name: 'Customer Authentication',
    description: 'API endpoints for customer authentication operations'
)]
class CustomerRegistrationControllerDocs extends CustomerRegistrationController
{
    #[OA\Post(
        path: '/api/customer/register',
        operationId: 'customerRegister',
        summary: 'Register a new customer',
        description: 'Create a new customer account. Returns authentication token immediately upon successful registration.',
        tags: ['Customer Authentication'],
        security: []
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            required: ['first_name', 'last_name', 'email', 'phone', 'password', 'password_confirmation'],
            properties: [
                new OA\Property(property: 'first_name', type: 'string', example: 'John'),
                new OA\Property(property: 'last_name', type: 'string', example: 'Doe'),
                new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                new OA\Property(property: 'phone', type: 'string', example: '+**********'),
                new OA\Property(
                    property: 'password', 
                    type: 'string', 
                    format: 'password', 
                    example: 'StrongPass123!',
                    description: 'Must contain at least 8 characters, one uppercase letter, one lowercase letter, one number, and one symbol'
                ),
                new OA\Property(
                    property: 'password_confirmation', 
                    type: 'string', 
                    format: 'password', 
                    example: 'StrongPass123!',
                    description: 'Must match the password field'
                )
            ]
        )
    )]
    #[OA\Response(
        response: 201,
        description: 'Customer registered successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'data',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'token', type: 'string', example: '1|abcdef123456...'),
                        new OA\Property(
                            property: 'user',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 1),
                                new OA\Property(property: 'first_name', type: 'string', example: 'John'),
                                new OA\Property(property: 'last_name', type: 'string', example: 'Doe'),
                                new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                                new OA\Property(property: 'phone', type: 'string', example: '+**********'),
                                new OA\Property(property: 'email_verified_at', type: 'string', format: 'date-time', nullable: true),
                                new OA\Property(property: 'flagged', type: 'boolean', example: false),
                                new OA\Property(property: 'code', type: 'string', example: 'ABC123XYZ'),
                                new OA\Property(property: 'created_at', type: 'string', format: 'date-time'),
                                new OA\Property(property: 'updated_at', type: 'string', format: 'date-time')
                            ]
                        )
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Validation errors',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'validation_error',
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'email',
                            type: 'array',
                            items: new OA\Items(type: 'string'),
                            example: ['The email has already been taken.']
                        ),
                        new OA\Property(
                            property: 'phone',
                            type: 'array',
                            items: new OA\Items(type: 'string'),
                            example: ['The phone has already been taken.']
                        ),
                        new OA\Property(
                            property: 'password',
                            type: 'array',
                            items: new OA\Items(type: 'string'),
                            example: ['The password must contain at least one uppercase and one lowercase letter.']
                        )
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: 'Server error',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'An error occurred during registration')
            ]
        )
    )]
    public function register(Request $request): JsonResponse|false|string
    {
        return parent::register($request);
    }
}
