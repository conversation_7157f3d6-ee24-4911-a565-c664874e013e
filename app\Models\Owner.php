<?php

namespace App\Models;

use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;

class Owner extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'email_verified_at',
        'avatar',
        'country_id',
        'state_id',
        'city_id',
        'approved',
        'approval_comment',
        'rejected',
        'rejection_comment',
        'rejection_reason',
        'balance',
        'commission',
        'email_verified',
        'phone_verified',
        'last_login',
        'send_next_otp_sms_after',
        'password_reset_code',
        'password',
        'code'
    ];

    protected $hidden = [
        'password',
        'remember_token',
        'deleted_at'
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'approved' => 'boolean',
        'rejected' => 'boolean',
        'balance' => 'float',
        'commission' => 'float',
        'send_next_otp_sms_after' => 'datetime',
    ];
}
