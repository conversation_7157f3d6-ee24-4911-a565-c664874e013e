<?php

namespace App\OpenApi\Owner\Auth;

use OpenApi\Attributes as OA;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Owner\OwnerAuth\OwnerForgottenPasswordController;

#[OA\Tag(
    name: 'Owner Authentication',
    description: 'API endpoints for owner authentication operations'
)]
class OwnerForgottenPasswordControllerDocs extends OwnerForgottenPasswordController
{
    #[OA\Post(
        path: '/api/owner/password/reset-code',
        operationId: 'ownerSendResetCode',
        summary: 'Send password reset code',
        description: 'Send a password reset code to owner\'s email address',
        tags: ['Owner Authentication'],
        security: []
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            required: ['email'],
            properties: [
                new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Reset code sent successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'success', type: 'string', example: 'Password reset code has been sent to your email')
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Failed to send reset code',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'No account found with this email address')
            ]
        )
    )]
    public function sendResetCode(Request $request): JsonResponse
    {
        return parent::sendResetCode($request);
    }

    #[OA\Post(
        path: '/api/owner/password/verify-code',
        operationId: 'ownerVerifyResetCode',
        summary: 'Verify reset code',
        description: 'Verify the password reset code sent to owner\'s email',
        tags: ['Owner Authentication'],
        security: []
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            required: ['email', 'code'],
            properties: [
                new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                new OA\Property(property: 'code', type: 'string', minLength: 6, maxLength: 6, example: '123456')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Code verified successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'success', type: 'string', example: 'Reset code verified successfully')
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Invalid or expired code'
    )]
    public function verifyResetCode(Request $request): JsonResponse
    {
        return parent::verifyResetCode($request);
    }

    #[OA\Post(
        path: '/api/owner/password/reset',
        operationId: 'ownerResetPassword',
        summary: 'Reset password',
        description: 'Reset owner\'s password using the verified reset code',
        tags: ['Owner Authentication'],
        security: []
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            required: ['email', 'code', 'password', 'password_confirmation'],
            properties: [
                new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                new OA\Property(property: 'code', type: 'string', minLength: 6, maxLength: 6, example: '123456'),
                new OA\Property(
                    property: 'password', 
                    type: 'string', 
                    format: 'password', 
                    example: 'NewPassword123!',
                    description: 'Must contain at least 8 characters, one uppercase letter, one lowercase letter, and one number'
                ),
                new OA\Property(property: 'password_confirmation', type: 'string', format: 'password', example: 'NewPassword123!')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Password reset successful',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'success', type: 'string', example: 'Password has been reset successfully')
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Reset failed',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Invalid or expired reset code')
            ]
        )
    )]
    public function resetPassword(Request $request): JsonResponse
    {
        return parent::resetPassword($request);
    }
}
